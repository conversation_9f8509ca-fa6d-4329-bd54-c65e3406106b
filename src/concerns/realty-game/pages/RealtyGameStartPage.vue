<template>
  <div class="realty-game-start-page">
    <q-no-ssr>

      <div class="max-ctr q-pa-sm rgsp">
        <!-- Loading State -->
        <div v-if="isLoading"
             class="loading-container text-center q-pa-xl">
          <q-spinner color="primary"
                     size="3em" />
          <div class="q-mt-md text-h6">Loading Properties...</div>
          <div class="text-body2 text-grey-7">Preparing your price guessing challenge</div>
        </div>

        <!-- Error State -->
        <div v-else-if="error"
             class="error-container text-center q-pa-xl">
          <q-icon name="error"
                  color="negative"
                  size="3em" />
          <div class="q-mt-md text-h6 text-negative">Failed to Load Properties</div>
          <div class="text-body2 text-grey-7 q-mb-lg">{{ error }}</div>
          <q-btn color="primary"
                 label="Try Again"
                 @click="initializeGame" />
        </div>
        <div v-else-if="totalProperties < 1"
             class="loading-container text-center q-pa-xl">
          <!-- <q-spinner color="primary"
                     size="3em" /> -->
          <div class="q-mt-md text-h6">Sorry, this price guessing challenge is not available yet</div>
          <div class="text-body2 text-grey-7">Please check again later</div>
        </div>
        <!-- Game Start -->
        <div v-else
             class="game-start-container">
          <!-- Welcome Section -->
          <div class="welcome-section text-center q-mb-xl">
            <div class="welcome-icon q-mb-lg">
              <q-icon name="home"
                      color="primary"
                      size="4em" />
            </div>
            <h1 class="text-h3 text-weight-bold text-primary q-mb-md">
              {{ gameTitle }}
            </h1>
            <p class="text-h6 text-grey-7 q-mb-lg">
              Test your property valuation skills with {{ totalProperties }} real properties
            </p>
            <div class="challenge-stats">
              <div class="row q-col-gutter-md justify-center">
                <div class="col-auto">
                  <q-chip color="primary"
                          text-color="white"
                          icon="home">
                    {{ totalProperties }} Properties
                  </q-chip>
                </div>
                <div class="col-auto">
                  <q-chip color="secondary"
                          text-color="white"
                          icon="timer">
                    ~{{ Math.ceil(totalProperties * .5) }} minutes
                  </q-chip>
                </div>
                <div class="col-auto">
                  <q-chip color="positive"
                          text-color="white"
                          icon="emoji_events">
                    Score up to {{ totalProperties * 100 }} points
                  </q-chip>
                </div>
              </div>
            </div>
          </div>

          <!-- How It Works -->
          <q-card class="how-it-works-card q-mb-xl"
                  flat
                  bordered>
            <q-card-section class="q-pa-lg">
              <div class="text-h5 text-weight-medium text-center q-mb-lg">
                <q-icon name="help_outline"
                        color="primary"
                        class="q-mr-sm" />
                How It Works
              </div>

              <div class="row q-col-gutter-lg">
                <div class="col-12 col-md-4">
                  <div class="step-card text-center">
                    <div class="step-number">1</div>
                    <div class="step-title">View Property</div>
                    <div class="step-description">
                      See photos and details of real properties currently for sale
                    </div>
                  </div>
                </div>
                <div class="col-12 col-md-4">
                  <div class="step-card text-center">
                    <div class="step-number">2</div>
                    <div class="step-title">Make Your Guess</div>
                    <div class="step-description">
                      Estimate the asking price based on what you see
                    </div>
                  </div>
                </div>
                <div class="col-12 col-md-4">
                  <div class="step-card text-center">
                    <div class="step-number">3</div>
                    <div class="step-title">Get Your Score</div>
                    <div class="step-description">
                      See how close you were and compare with other players
                    </div>
                  </div>
                </div>
              </div>
            </q-card-section>
          </q-card>

          <!-- User Info Collection -->
          <q-card class="user-info-card q-mb-xl"
                  flat
                  bordered>
            <q-card-section class="q-pa-lg">
              <div class="text-h6 text-weight-medium q-mb-md">
                <q-icon name="person"
                        color="primary"
                        class="q-mr-sm" />
                Player Information...
              </div>

              <div class="row q-col-gutter-md">
                <div class="col-12 col-md-6">
                  <q-input v-model="playerName"
                           label="Your name"
                           outlined
                           dense
                           placeholder="Enter your name or leave blank"
                           hint="This helps us show you how you compare to other players">
                    <template v-slot:prepend>
                      <q-icon name="person" />
                    </template>
                  </q-input>
                </div>
                <div class="col-12 col-md-6 currency-selector">
                  <q-select v-model="selectedCurrency"
                            :options="currencyOptions"
                            option-label="label"
                            option-value="code"
                            emit-value
                            map-options
                            outlined
                            dense
                            label="Currency"
                            hint="Choose your preferred currency for prices">
                    <template v-slot:prepend>
                      <q-icon name="currency_exchange" />
                    </template>
                    <template v-slot:option="scope">
                      <q-item v-bind="scope.itemProps">
                        <q-item-section avatar>
                          <span class="currency-symbol">{{ scope.opt.symbol }}</span>
                        </q-item-section>
                        <q-item-section>
                          <q-item-label>{{ scope.opt.name }}</q-item-label>
                          <q-item-label caption>{{ scope.opt.code }}</q-item-label>
                        </q-item-section>
                      </q-item>
                    </template>
                    <template v-slot:selected>
                      <span class="selected-currency">
                        {{ selectedCurrencyData?.symbol }} {{ selectedCurrencyData?.name }}
                      </span>
                    </template>
                  </q-select>
                </div>
              </div>

              <div class="text-caption text-grey-6 q-mt-md">
                Your guesses will be saved and compared with other players. No personal information is required.
              </div>
            </q-card-section>
          </q-card>

          <!-- Start Button -->
          <div class="start-section text-center">
            <q-btn color="primary"
                   size="xl"
                   rounded
                   unelevated
                   icon="play_arrow"
                   label="Start Challenge"
                   @click="startGame"
                   class="start-button" />

            <div class="text-caption text-grey-6 q-mt-md">
              Click to begin your property price challenge
            </div>
          </div>
        </div>
      </div>
    </q-no-ssr>
  </div>
</template>

<script setup>
import { ref, computed, watch, getCurrentInstance } from 'vue'
import { useRouter } from 'vue-router'
import { useQuasar } from 'quasar'
import { useRealtyGame } from '../composables/useRealtyGame'
import { useRealtyGameStorage } from '../composables/useRealtyGameStorage'
import { useCurrencyConverter } from '../composables/useCurrencyConverter'

const $router = useRouter()
const $q = useQuasar()

// Get current instance to access global properties like $ahoy
const instance = getCurrentInstance()

// Initialize the price guess composable
const {
  isLoading,
  error,
  // gameDefaultCurrency,
  fetchRealtyGameData
} = useRealtyGame()

// Initialize the storage composable
const {
  getOrCreateSessionId,
  saveSessionData,
  getSessionData,
  saveCurrencySelection,
  getCurrencySelection
} = useRealtyGameStorage()

// Initialize the currency converter
const {
  selectedCurrency,
  availableCurrencies,
  selectedCurrencyData,
  setCurrency
} = useCurrencyConverter()

// Props
const props = defineProps({
  gameTitle: {
    type: String,
    required: false
  },
  gameDefaultCurrency: {
    type: String,
    required: false
  },
  totalProperties: {
    type: Number,
    required: false
  }
})

// Local state - Initialize playerName from session data
const playerName = ref(getSessionData().playerName || '')

// Initialize currency from session data
const sessionData = getSessionData()
if (sessionData.selectedCurrency) {
  // Use currency from current session data
  setCurrency(sessionData.selectedCurrency)
} else {
  // No session currency set, check for stored currency preference
  const storedCurrency = getCurrencySelection()

  // If stored currency is just the default fallback, use game's default currency instead
  if (storedCurrency === 'GBP' && props.gameDefaultCurrency && props.gameDefaultCurrency !== 'GBP') {
    // Use game's default currency as it's more specific than the generic GBP fallback
    setCurrency(props.gameDefaultCurrency)
  } else {
    // Use stored currency selection (could be GBP if that's what user actually selected)
    setCurrency(storedCurrency)
  }
}

// Computed properties for currency options
const currencyOptions = computed(() => {
  return availableCurrencies.value.map(currency => ({
    code: currency.code,
    name: currency.name,
    symbol: currency.symbol,
    label: `${currency.symbol} ${currency.name} (${currency.code})`
  }))
})

// Watch playerName and save to session data when it changes
watch(playerName, (newValue) => {
  saveSessionData({
    playerName: newValue || 'Anonymous Player',
    totalProperties: props.totalProperties || 0,
    gameTitle: props.gameTitle || 'Property Price Challenge',
    startedAt: new Date().toISOString()
  })
})

// Watch selectedCurrency and save to session data when it changes
watch(selectedCurrency, (newValue) => {
  if (newValue) {
    saveCurrencySelection(newValue)
    saveSessionData({
      selectedCurrency: newValue
    })
  }
})

// Methods
const initializeGame = async () => {
  try {
    await fetchRealtyGameData()
  } catch (err) {
    $q.notify({
      color: 'negative',
      message: 'Failed to load property data',
      icon: 'error'
    })
  }
}

const startGame = () => {
  // Get or create session ID from storage
  const gameSessionId = getOrCreateSessionId()

  // Save session metadata
  saveSessionData({
    playerName: playerName.value || 'Anonymous Player',
    totalProperties: props.totalProperties || 0,
    gameTitle: props.gameTitle || 'Property Price Challenge',
    selectedCurrency: selectedCurrency.value,
    startedAt: new Date().toISOString()
  })

  // Save currency selection
  saveCurrencySelection(selectedCurrency.value)

  // // Track new game session with Ahoy (client-side only)
  // if (process.env.CLIENT && instance?.appContext?.config?.globalProperties?.$ahoy) {
  //   const ahoy = instance.appContext.config.globalProperties.$ahoy

  //   // Function to track the event
  //   const trackGameSession = (attempt = 1) => {
  //     try {
  //       const visitId = ahoy.getVisitId()
  //       const visitorId = ahoy.getVisitorId()

  //       console.log(`Ahoy tracking attempt ${attempt}:`, {
  //         visitId,
  //         visitorId,
  //         hasVisitId: !!visitId,
  //         hasVisitorId: !!visitorId
  //       })

  //       ahoy.track('New Game Session', {
  //         game_session_id: gameSessionId,
  //         guest_name: playerName.value || 'Anonymous Player',
  //         total_properties: props.totalProperties || 0,
  //         game_title: props.gameTitle || 'Property Price Challenge',
  //         selected_currency: selectedCurrency.value,
  //         started_at: new Date().toISOString()
  //       })

  //       console.log('Game session tracked successfully')
  //     } catch (error) {
  //       console.warn('Failed to track game session:', error)
  //     }
  //   }

  //   // Function to wait for Ahoy to be ready with retries
  //   const waitForAhoyAndTrack = (maxAttempts = 5, currentAttempt = 1) => {
  //     const visitId = ahoy.getVisitId()
  //     const visitorId = ahoy.getVisitorId()

  //     if (visitId && visitorId) {
  //       // Tokens are available, track immediately
  //       trackGameSession(currentAttempt)
  //     } else if (currentAttempt < maxAttempts) {
  //       // Tokens not ready, wait and try again
  //       console.log(`Ahoy tokens not ready (attempt ${currentAttempt}/${maxAttempts}), waiting...`)
  //       setTimeout(() => {
  //         waitForAhoyAndTrack(maxAttempts, currentAttempt + 1)
  //       }, 200 * currentAttempt) // Exponential backoff: 200ms, 400ms, 600ms, etc.
  //     } else {
  //       // Max attempts reached, track anyway
  //       console.warn('Ahoy tokens still not available after all retries, tracking anyway')
  //       trackGameSession(currentAttempt)
  //     }
  //   }

  //   // Start the tracking process
  //   waitForAhoyAndTrack()
  // }

  // Navigate to first property with session ID
  $router.push({
    name: 'rPriceGameProperty',
    params: {
      propertyIndex: 0
    },
    query: {
      session: gameSessionId,
      ...(playerName.value && { name: playerName.value })
    }
  })
}
</script>

<style scoped>
.realty-game-start-page {
  min-height: 100vh;
}

.max-ctr {
  max-width: 800px;
  margin: 0 auto;
}

.loading-container,
.error-container {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.game-start-container {
  padding-top: 2rem;
}

.welcome-section {
  background: white;
  border-radius: 12px;
  padding: 3rem 2rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.welcome-icon {
  border-radius: 50%;
  width: 120px;
  height: 120px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto;
}

.challenge-stats {
  margin-top: 2rem;
}

.how-it-works-card {
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.step-card {
  padding: 1.5rem;
}

.step-number {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background: linear-gradient(135deg, #1976d2, #42a5f5);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  font-weight: bold;
  margin: 0 auto 1rem;
}

.step-title {
  font-size: 1.1rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
  color: #333;
}

.step-description {
  color: #666;
  line-height: 1.5;
}

.user-info-card {
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.start-section {
  background: white;
  border-radius: 12px;
  padding: 3rem 2rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.start-button {
  padding: 1rem 3rem;
  font-size: 1.2rem;
  font-weight: 600;
  text-transform: none;
}

.currency-symbol {
  font-weight: bold;
  font-size: 1.1rem;
  min-width: 30px;
  text-align: center;
}

.selected-currency {
  font-weight: 500;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .welcome-section {
    padding: 2rem 1rem;
  }

  .start-section {
    padding: 2rem 1rem;
  }

  .start-button {
    width: 100%;
    max-width: 300px;
  }
}
</style>